from funcs import get_raw_data, pre_process_data, process_data
from plots import Plotter


from plots import Plotter

plotter = Plotter()
train_data=pre_process_data(get_raw_data('1.3',2e5))
# val_data=pre_process_data(get_raw_data('1.2',2e5))

plotter.plot_loading_graphs(get_raw_data('1.3',2e5))
# plotter.plot_loading_graphs(get_raw_data('1.2',2e5))
plotter.plot_data_hisograms(train_data, figsize=(7.5, 5), use_log_scale_yaxis=False)
# plotter.plot_data_scatter(train_data, val_data, axis_dict={'x':1, 'y':2, 'z':7})
plotter.plot_data_scatter(train_data, axis_dict={'x':0, 'y':2, 'z':7})
plotter.plot_data_scatter(train_data, axis_dict={'x':0, 'y':3, 'z':7})

