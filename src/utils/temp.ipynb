from plots import Plotter
plotter = Plotter()

from funcs import get_raw_data, compute_psi, compute_invariants, compute_deviatoric, compute_eps_pl, pre_process_data
import numpy as np
train_data_raw = get_raw_data('1.0',2e5)
train_data = pre_process_data(train_data_raw)
val_data = pre_process_data(get_raw_data('1.2',2e5))
# test_data = pre_process_data(get_raw_data('12.3',2e5))

plotter.plot_loading_graphs(train_data_raw)
plotter.plot_loading_graphs(get_raw_data('1.2',2e5))
plotter.plot_data_hisograms(train_data, val_data, figsize=(8, 5), use_log_scale_yaxis=True)
plotter.plot_data_scatter(train_data, val_data, axis_dict={'x':0, 'y':2, 'z':7})


plotter.plot_data_scatter(pre_processed_data, axis_dict={'x':1, 'y':3, 'z':8})

I2 = pre_processed_data[:, 1]
np.all(np.diff(I2) <= 0)

((4.0/3.0) * 1.2909718861919828e-05) ** 0.5

