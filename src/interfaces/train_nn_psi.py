"""
This script serves as the training interface for the model.

Date: 2025-07-08
Author: <PERSON><PERSON><PERSON>
"""
#%% ---- Import Required Libraries ----
import random
import sys

import numpy as np
import tensorflow as tf

sys.path.append('/home/<USER>/phd/tasks/nn_mat_model/src/utils')

from funcs import get_raw_data, pre_process_data, process_data, save_train_history
from nn_model import NN_Model
from plots import Plotter

#%% ---- Set Random Seed ----
seed = 42
random.seed(seed)
np.random.seed(seed)
tf.random.set_seed(seed)

#%% ---- Load Data ----
E=2e5
dataset_name = '1'
train_data=pre_process_data(get_raw_data(f'1.0',E))
val_data=pre_process_data(get_raw_data(f'1.2',E))

n_train_data, n_val_data, norm_params = process_data(train_data, val_data)

# %% Model Configuration
activation_dict = {
    'custom_act': 'custom_act',
    'relu': tf.keras.activations.relu,
    'leaky_relu': tf.keras.layers.LeakyReLU(),
    'sigmoid': tf.keras.activations.sigmoid,
    'tanh': tf.keras.activations.tanh,
    'elu': tf.keras.activations.elu,
}

# TODO: Determin the hyperparameters:
            # 1- Number of hidden layers and their dimensions. For example: [48] means one hidden layer with 48 nodes.
            # 2- Initial Learning rate, number of epochs, and batch size.
            # 3- Activation function.
            # 4- Learning rate schedule type: 'exponential', 'cosine', or 'constant'

hidden_dims = [64, 48]
learningRate=1e-3; nEpochs = 2500; bSize = 32
activation_function = activation_dict['leaky_relu']
lr_schedule_type = 'cosine'


model_psi = NN_Model(norm_params=norm_params, hidden_dims=hidden_dims, activation_fn=activation_function)
history = NN_Model.train_model(model_psi,
                          train_data=n_train_data,
                          val_data=n_val_data,
                          LearningRate=learningRate, nEpochs=nEpochs, bSize=bSize, silent_training=False,
                          lr_schedule_type=lr_schedule_type)

# %% Save Model
model_path = NN_Model.save_model(model_psi, dataset_name=f'{dataset_name}',
                            learning_rate=learningRate, num_epochs=nEpochs, batch_size=bSize,
                            norm_params=norm_params, save_dir='../../saved_models/psi_max')

save_train_history(history, model_path, filename='training_history')
# %% Plot
plotter = Plotter()
plotter.plot_loss(history=history, save_path=model_path)