import sys

import numpy as np
import tensorflow as tf

sys.path.append('/home/<USER>/phd/tasks/nn_mat_model/src/utils')

from funcs import get_raw_data, pre_process_data, process_data, normalize_data, normalize_stress_components, apply_mixed_normalization
from nn_model import NN_Model
from plots import Plotter

#%% ---- Load Data ----
tf.keras.backend.set_floatx('float64')
GLOBAL_DTYPE = tf.float64

model_dir = '../../saved_models/psi_max/...'
loaded_model, metadata = NN_Model.load_model(model_dir)

#%% ---- Inference ----
E=2e5
test_data = pre_process_data(get_raw_data('1.3',E))
